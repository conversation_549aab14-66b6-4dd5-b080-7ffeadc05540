# Examples

This section contains practical examples and code samples for using Mermaid Render in various scenarios.

## Basic Examples

### [Simple Diagrams](basic/simple-diagrams.md)
Basic examples for each diagram type with minimal code.

### [Configuration Examples](basic/configuration.md)
Different ways to configure Mermaid Render for your needs.

### [Theme Examples](basic/themes.md)
Using built-in themes and creating custom themes.

## Diagram Type Examples

### [Flowchart Examples](diagrams/flowcharts.md)
- Process flows
- Decision trees
- Workflow diagrams
- Complex branching scenarios

### [Sequence Diagram Examples](diagrams/sequence.md)
- API interactions
- User authentication flows
- Microservice communication
- Error handling scenarios

### [Class Diagram Examples](diagrams/class.md)
- Object-oriented design
- Inheritance hierarchies
- Interface implementations
- Design patterns

### [State Diagram Examples](diagrams/state.md)
- State machines
- Workflow states
- Game states
- UI component states

### [ER Diagram Examples](diagrams/er.md)
- Database schemas
- Entity relationships
- Data modeling
- Migration planning

## Real-World Use Cases

### [Software Architecture](use-cases/architecture.md)
Document system architecture and component relationships.

```python
from mermaid_render import FlowchartDiagram, <PERSON><PERSON><PERSON><PERSON><PERSON>

def create_microservices_architecture():
    diagram = FlowchartDiagram(title="Microservices Architecture")
    
    # Add services
    diagram.add_node("gateway", "API Gateway", shape="rectangle")
    diagram.add_node("auth", "Auth Service", shape="rectangle")
    diagram.add_node("user", "User Service", shape="rectangle")
    diagram.add_node("order", "Order Service", shape="rectangle")
    diagram.add_node("payment", "Payment Service", shape="rectangle")
    diagram.add_node("db", "Database", shape="cylinder")
    
    # Add connections
    diagram.add_edge("gateway", "auth", "authenticate")
    diagram.add_edge("gateway", "user", "user ops")
    diagram.add_edge("gateway", "order", "order ops")
    diagram.add_edge("order", "payment", "process payment")
    diagram.add_edge("user", "db", "user data")
    diagram.add_edge("order", "db", "order data")
    
    renderer = MermaidRenderer(theme="neutral")
    return renderer.render(diagram, format="svg")
```

### [API Documentation](use-cases/api-docs.md)
Generate API flow diagrams automatically.

```python
from mermaid_render import SequenceDiagram

def document_api_flow(endpoints):
    sequence = SequenceDiagram(title="API Flow Documentation")
    
    sequence.add_participant("client", "Client")
    sequence.add_participant("api", "API Server")
    sequence.add_participant("db", "Database")
    
    for endpoint in endpoints:
        sequence.add_message("client", "api", f"{endpoint.method} {endpoint.path}")
        if endpoint.requires_auth:
            sequence.add_message("api", "db", "Validate token")
        sequence.add_message("api", "db", f"Query {endpoint.resource}")
        sequence.add_message("db", "api", "Results", message_type="return")
        sequence.add_message("api", "client", "Response", message_type="return")
    
    return sequence
```

### [Business Process Modeling](use-cases/business.md)
Model business workflows and processes.

### [Database Design](use-cases/database.md)
Design and document database schemas.

### [Project Planning](use-cases/project-planning.md)
Create project timelines and resource planning diagrams.

## Integration Examples

### [Web Framework Integration](integration/web-frameworks.md)
Examples for Flask, Django, FastAPI, and other web frameworks.

#### Flask Example
```python
from flask import Flask, request, jsonify, render_template
from mermaid_render import quick_render, ValidationError

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('diagram_editor.html')

@app.route('/api/render', methods=['POST'])
def render_diagram():
    try:
        data = request.get_json()
        diagram_code = data.get('diagram', '')
        format = data.get('format', 'svg')
        theme = data.get('theme', 'default')
        
        result = quick_render(
            diagram_code,
            format=format,
            theme=theme
        )
        
        return jsonify({
            'success': True,
            'content': result,
            'format': format
        })
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': 'Validation failed',
            'details': str(e)
        }), 400
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Rendering failed',
            'details': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True)
```

### [CLI Applications](integration/cli.md)
Command-line tools and scripts using Mermaid Render.

### [Desktop Applications](integration/desktop.md)
GUI applications with diagram generation capabilities.

### [CI/CD Integration](integration/cicd.md)
Automated diagram generation in build pipelines.

## Advanced Examples

### [Custom Renderers](advanced/custom-renderers.md)
Creating custom rendering backends and output formats.

### [Plugin Development](advanced/plugins.md)
Extending Mermaid Render with custom functionality.

### [Performance Optimization](advanced/performance.md)
Optimizing rendering performance for large-scale applications.

### [Caching Strategies](advanced/caching.md)
Implementing effective caching for improved performance.

```python
from mermaid_render.cache import create_cache_manager, CacheStrategy

# Redis caching example
cache = create_cache_manager(
    backend="redis",
    host="localhost",
    port=6379,
    strategy=CacheStrategy.LRU,
    max_size=1000
)

# Warm cache with frequently used diagrams
cache.warm_cache([
    ("user_flow", user_flow_diagram),
    ("api_docs", api_documentation_diagram),
    ("architecture", system_architecture_diagram)
])

# Use cached rendering
def render_with_cache(diagram, format="svg"):
    cache_key = f"{diagram.get_hash()}_{format}"
    
    # Try cache first
    cached_result = cache.get(cache_key)
    if cached_result:
        return cached_result
    
    # Render and cache
    result = renderer.render(diagram, format=format)
    cache.set(cache_key, result, ttl=3600)  # Cache for 1 hour
    
    return result
```

### [AI-Powered Generation](advanced/ai-generation.md)
Using AI features for diagram generation and optimization.

## Testing Examples

### [Unit Testing](testing/unit-tests.md)
Testing applications that use Mermaid Render.

```python
import unittest
from unittest.mock import patch, MagicMock
from mermaid_render import FlowchartDiagram, MermaidRenderer
from mermaid_render.exceptions import ValidationError

class TestDiagramGeneration(unittest.TestCase):
    
    def setUp(self):
        self.renderer = MermaidRenderer()
        self.flowchart = FlowchartDiagram()
    
    def test_simple_flowchart_creation(self):
        """Test creating a simple flowchart."""
        self.flowchart.add_node("A", "Start")
        self.flowchart.add_node("B", "End")
        self.flowchart.add_edge("A", "B")
        
        mermaid_code = self.flowchart.to_mermaid()
        self.assertIn("A[Start]", mermaid_code)
        self.assertIn("B[End]", mermaid_code)
        self.assertIn("A --> B", mermaid_code)
    
    @patch('mermaid_render.core.requests.post')
    def test_rendering_with_mock(self, mock_post):
        """Test rendering with mocked HTTP requests."""
        mock_response = MagicMock()
        mock_response.content = b'<svg>test</svg>'
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        result = self.renderer.render(self.flowchart, format="svg")
        self.assertEqual(result, '<svg>test</svg>')
    
    def test_validation_error_handling(self):
        """Test handling of validation errors."""
        invalid_code = "invalid mermaid syntax"
        
        with self.assertRaises(ValidationError):
            self.renderer.render_raw(invalid_code)

if __name__ == '__main__':
    unittest.main()
```

### [Integration Testing](testing/integration-tests.md)
End-to-end testing strategies.

### [Performance Testing](testing/performance.md)
Benchmarking and performance testing.

## Code Snippets

### [Common Patterns](snippets/patterns.md)
Reusable code patterns and utilities.

### [Helper Functions](snippets/helpers.md)
Utility functions for common tasks.

### [Error Handling](snippets/error-handling.md)
Robust error handling patterns.

## Interactive Examples

### [Jupyter Notebooks](interactive/notebooks.md)
Using Mermaid Render in Jupyter notebooks and data science workflows.

### [Web Demos](interactive/web-demos.md)
Interactive web-based diagram editors and viewers.

### [Live Examples](interactive/live.md)
Try examples directly in your browser.

## Community Examples

### [User Contributions](community/contributions.md)
Examples contributed by the community.

### [Showcase Projects](community/showcase.md)
Real projects using Mermaid Render.

### [Templates Library](community/templates.md)
Community-contributed diagram templates.

## Getting Started with Examples

1. **Browse by Category**: Use the navigation above to find examples for your use case
2. **Copy and Modify**: All examples are designed to be copied and adapted
3. **Run Locally**: Clone the repository and run examples in the `examples/` directory
4. **Contribute**: Share your own examples with the community

## Example Repository Structure

```
examples/
├── basic/                 # Simple, introductory examples
├── diagrams/             # Examples for each diagram type
├── use-cases/            # Real-world scenarios
├── integration/          # Framework and tool integrations
├── advanced/             # Complex features and optimizations
├── testing/              # Testing strategies and examples
├── snippets/             # Reusable code snippets
├── interactive/          # Jupyter notebooks and web demos
└── community/            # Community contributions
```

## Running Examples

```bash
# Clone the repository
git clone https://github.com/mermaid-render/mermaid-render.git
cd mermaid-render

# Install with examples dependencies
pip install -e ".[dev,examples]"

# Run basic examples
python examples/basic/simple_flowchart.py

# Run advanced examples
python examples/advanced/caching_demo.py

# Start interactive demo
python examples/interactive/web_demo.py
```

## Contributing Examples

We welcome example contributions! See our [Contributing Guide](../contributing/index.md) for details on:

- Example code standards
- Documentation requirements
- Testing guidelines
- Submission process

---

**Need help with a specific use case?** Check our [GitHub Discussions](https://github.com/mermaid-render/mermaid-render/discussions) or [create an issue](https://github.com/mermaid-render/mermaid-render/issues/new).
